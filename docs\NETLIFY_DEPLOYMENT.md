# Netlify Deployment Guide

## Environment Variables Setup

Add these environment variables in your Netlify dashboard (Site Settings > Environment Variables):

```bash
# Database
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Stripe
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# App Configuration
NEXT_PUBLIC_APP_URL=https://yourapp.netlify.app
NEXTAUTH_URL=https://yourapp.netlify.app
NEXTAUTH_SECRET=your_nextauth_secret

# Domain Management
CLOUDFLARE_API_TOKEN=your_cloudflare_token
CLOUDFLARE_ZONE_ID=your_zone_id

# Email (Optional)
RESEND_API_KEY=your_resend_api_key
```

## Deployment Steps

### 1. Install Netlify CLI

```bash
npm install -g netlify-cli
```

### 2. Login to Netlify

```bash
netlify login
```

### 3. Initialize Site

```bash
netlify init
```

### 4. Deploy

```bash
# Deploy to preview
netlify deploy

# Deploy to production
netlify deploy --prod
```

## Custom Domain Setup

### Subdomain Routing for Restaurants

1. **DNS Configuration**

   - Add CNAME record: `*.yourdomain.com` → `yourapp.netlify.app`
   - Add A record: `yourdomain.com` → Netlify's IP

2. **Netlify Domain Settings**
   - Go to Site Settings > Domain Management
   - Add `yourdomain.com` as primary domain
   - Add `*.yourdomain.com` for subdomains

### Custom Domain for Individual Restaurants

1. **Restaurant owners add CNAME**:

   ```
   CNAME: restaurantdomain.com → yourapp.netlify.app
   ```

2. **Netlify Edge Functions** (create in `/netlify/edge-functions/domain-router.ts`):

   ```typescript
   import type { Context } from "@netlify/edge-functions";

   export default async (request: Request, context: Context) => {
     const url = new URL(request.url);
     const hostname = url.hostname;

     // Check if it's a custom domain
     if (
       !hostname.includes("netlify.app") &&
       !hostname.includes("yourdomain.com")
     ) {
       // Look up restaurant by custom domain in Supabase
       const restaurant = await fetchRestaurantByDomain(hostname);

       if (restaurant) {
         // Rewrite to restaurant page
         url.pathname = `/restaurant/${restaurant.slug}${url.pathname}`;
         return context.rewrite(url);
       }
     }

     return context.next();
   };
   ```

## Netlify Functions for API Routes

Netlify automatically handles Next.js API routes, but for additional serverless functions:

### Create `/netlify/functions/webhook.ts`

```typescript
import { Handler } from "@netlify/functions";
import Stripe from "stripe";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: "2023-10-16",
});

export const handler: Handler = async (event, context) => {
  if (event.httpMethod !== "POST") {
    return { statusCode: 405, body: "Method Not Allowed" };
  }

  try {
    const sig = event.headers["stripe-signature"];
    const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET!;

    const stripeEvent = stripe.webhooks.constructEvent(
      event.body!,
      sig!,
      endpointSecret
    );

    // Handle the event
    switch (stripeEvent.type) {
      case "customer.subscription.created":
        // Handle subscription creation
        break;
      case "customer.subscription.updated":
        // Handle subscription update
        break;
      default:
        console.log(`Unhandled event type ${stripeEvent.type}`);
    }

    return { statusCode: 200, body: "Success" };
  } catch (error) {
    console.error("Webhook error:", error);
    return { statusCode: 400, body: "Webhook Error" };
  }
};
```

## Performance Optimization

### 1. Build Settings

```toml
# In netlify.toml
[build]
  command = "npm run build"
  environment = { NODE_VERSION = "18" }

[build.processing]
  skip_processing = false

[build.processing.css]
  bundle = true
  minify = true

[build.processing.js]
  bundle = true
  minify = true

[build.processing.html]
  pretty_urls = true
```

### 2. Caching Headers

```toml
[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/_next/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
```

## Monitoring and Analytics

### 1. Netlify Analytics

- Enable in Site Settings > Analytics
- View real-time traffic and performance

### 2. Custom Monitoring

```typescript
// Add to your app
export function trackNetlifyEvent(eventName: string, properties?: any) {
  if (typeof window !== "undefined" && window.netlifyIdentity) {
    // Track custom events
    console.log("Event:", eventName, properties);
  }
}
```

## Advantages of Netlify for Restaurant SaaS

### ✅ Pros

- **Cost-effective**: More generous free tier
- **Better for static sites**: Excellent for marketing pages
- **Built-in forms**: Great for contact forms, reservations
- **A/B testing**: Native split testing capabilities
- **Analytics**: Free analytics included
- **Git-based workflow**: Simple deployment process

### ⚠️ Considerations

- **Next.js optimization**: Vercel has native Next.js optimization
- **Edge functions**: Limited compared to Vercel's edge runtime
- **Build times**: Slightly slower for complex Next.js apps
- **Serverless functions**: 10-second timeout vs Vercel's 30s

## Migration from Vercel

If you're migrating from Vercel:

1. **Export Vercel environment variables**:

   ```bash
   vercel env pull .env.local
   ```

2. **Update API endpoints**:

   ```typescript
   // Change from Vercel Edge Functions
   export const config = { runtime: "edge" };

   // To Netlify Edge Functions
   export default async (request: Request, context: Context) => {
     // Your logic here
   };
   ```

3. **Update deployment commands**:
   ```json
   {
     "scripts": {
       "deploy": "netlify deploy --prod",
       "deploy:preview": "netlify deploy"
     }
   }
   ```

## Troubleshooting

### Common Issues

1. **Build failures**:

   ```bash
   # Clear cache and rebuild
   netlify build --clear-cache
   ```

2. **Function timeouts**:

   ```toml
   [functions]
     timeout = 30  # Increase timeout
   ```

3. **Large bundle sizes**:
   ```javascript
   // next.config.js
   module.exports = {
     experimental: {
       outputFileTracingRoot: path.join(__dirname, "../../"),
     },
   };
   ```

## Cost Comparison

| Feature           | Netlify                | Vercel                 |
| ----------------- | ---------------------- | ---------------------- |
| **Builds**        | 300/month free         | 100/month free         |
| **Bandwidth**     | 100GB/month free       | 100GB/month free       |
| **Functions**     | 125k invocations/month | 100k invocations/month |
| **Team members**  | Unlimited              | 3 free, then $20/user  |
| **Build minutes** | 300/month              | 100/month              |

For a restaurant SaaS platform, Netlify can be more cost-effective, especially during the growth phase.
