import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { cn } from '@/lib/utils'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Restaurant SaaS Platform',
  description: 'Multi-tenant restaurant SaaS platform with subscription-based online menus',
  keywords: ['restaurant', 'menu', 'saas', 'subscription', 'multi-tenant'],
  authors: [{ name: 'Restaurant SaaS Team' }],
  creator: 'Restaurant SaaS Platform',
  publisher: 'Restaurant SaaS Platform',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={cn(
        'min-h-screen bg-background font-sans antialiased',
        inter.className
      )}>
        <div id="root">
          {children}
        </div>
      </body>
    </html>
  )
} 