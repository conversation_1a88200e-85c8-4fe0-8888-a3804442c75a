import Link from "next/link"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

interface AuthLayoutProps {
  children: React.ReactNode
  title: string
  description: string
  showBackToHome?: boolean
}

export function AuthLayout({ 
  children, 
  title, 
  description, 
  showBackToHome = true 
}: AuthLayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-red-50 to-rose-50">
      <div className="container relative min-h-screen flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-2 lg:px-0">
        {/* Left side - Branding */}
        <div className="relative hidden h-full flex-col bg-muted p-10 text-white lg:flex dark:border-r">
          <div className="absolute inset-0 bg-gradient-to-br from-orange-600 via-red-600 to-rose-600" />
          <div className="relative z-20 flex items-center text-lg font-medium">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="mr-2 h-6 w-6"
            >
              <path d="M3 2v6h6M21 12A9 9 0 1 1 8.5 2.5" />
            </svg>
            Restaurant SaaS
          </div>
          <div className="relative z-20 mt-auto">
            <blockquote className="space-y-2">
              <p className="text-lg">
                "This platform has transformed how we manage our restaurant's online presence. 
                Setting up our digital menu was incredibly easy and our customers love the experience."
              </p>
              <footer className="text-sm">Sofia Davis, Owner of Bella Vista</footer>
            </blockquote>
          </div>
        </div>

        {/* Right side - Auth Form */}
        <div className="lg:p-8">
          <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[400px]">
            {/* Mobile branding */}
            <div className="flex flex-col items-center space-y-2 text-center lg:hidden">
              <div className="flex items-center text-lg font-medium text-orange-600">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="mr-2 h-6 w-6"
                >
                  <path d="M3 2v6h6M21 12A9 9 0 1 1 8.5 2.5" />
                </svg>
                Restaurant SaaS
              </div>
            </div>

            <Card className="shadow-lg border-0">
              <CardHeader className="space-y-1">
                <CardTitle className="text-2xl text-center font-bold text-gray-900">
                  {title}
                </CardTitle>
                <CardDescription className="text-center text-gray-600">
                  {description}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                {children}
              </CardContent>
            </Card>

            {showBackToHome && (
              <div className="text-center">
                <Link
                  href="/"
                  className="text-sm text-gray-600 hover:text-orange-600 transition-colors"
                >
                  ← Back to homepage
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
} 