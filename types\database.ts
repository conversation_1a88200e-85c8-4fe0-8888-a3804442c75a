export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      organizations: {
        Row: {
          id: string
          name: string
          slug: string
          custom_domain: string | null
          description: string | null
          logo_url: string | null
          contact_email: string | null
          phone: string | null
          address: string | null
          city: string | null
          state: string | null
          country: string
          postal_code: string | null
          subscription_status: 'trial' | 'active' | 'cancelled' | 'past_due'
          plan_id: string | null
          stripe_customer_id: string | null
          trial_ends_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          slug: string
          custom_domain?: string | null
          description?: string | null
          logo_url?: string | null
          contact_email?: string | null
          phone?: string | null
          address?: string | null
          city?: string | null
          state?: string | null
          country?: string
          postal_code?: string | null
          subscription_status?: 'trial' | 'active' | 'cancelled' | 'past_due'
          plan_id?: string | null
          stripe_customer_id?: string | null
          trial_ends_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          custom_domain?: string | null
          description?: string | null
          logo_url?: string | null
          contact_email?: string | null
          phone?: string | null
          address?: string | null
          city?: string | null
          state?: string | null
          country?: string
          postal_code?: string | null
          subscription_status?: 'trial' | 'active' | 'cancelled' | 'past_due'
          plan_id?: string | null
          stripe_customer_id?: string | null
          trial_ends_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      users: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          organization_id: string | null
          role: 'owner' | 'admin' | 'manager' | 'staff'
          permissions: Json
          is_active: boolean
          last_login_at: string | null
          invited_by: string | null
          invited_at: string | null
          accepted_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          organization_id?: string | null
          role?: 'owner' | 'admin' | 'manager' | 'staff'
          permissions?: Json
          is_active?: boolean
          last_login_at?: string | null
          invited_by?: string | null
          invited_at?: string | null
          accepted_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          organization_id?: string | null
          role?: 'owner' | 'admin' | 'manager' | 'staff'
          permissions?: Json
          is_active?: boolean
          last_login_at?: string | null
          invited_by?: string | null
          invited_at?: string | null
          accepted_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "users_id_fkey"
            columns: ["id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "users_organization_id_fkey"
            columns: ["organization_id"]
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "users_invited_by_fkey"
            columns: ["invited_by"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      menus: {
        Row: {
          id: string
          organization_id: string
          name: string
          description: string | null
          is_active: boolean
          is_published: boolean
          display_order: number
          theme: Json
          settings: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          name: string
          description?: string | null
          is_active?: boolean
          is_published?: boolean
          display_order?: number
          theme?: Json
          settings?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          name?: string
          description?: string | null
          is_active?: boolean
          is_published?: boolean
          display_order?: number
          theme?: Json
          settings?: Json
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "menus_organization_id_fkey"
            columns: ["organization_id"]
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          }
        ]
      }
      menu_categories: {
        Row: {
          id: string
          menu_id: string
          name: string
          description: string | null
          image_url: string | null
          is_active: boolean
          display_order: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          menu_id: string
          name: string
          description?: string | null
          image_url?: string | null
          is_active?: boolean
          display_order?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          menu_id?: string
          name?: string
          description?: string | null
          image_url?: string | null
          is_active?: boolean
          display_order?: number
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "menu_categories_menu_id_fkey"
            columns: ["menu_id"]
            referencedRelation: "menus"
            referencedColumns: ["id"]
          }
        ]
      }
      menu_items: {
        Row: {
          id: string
          category_id: string
          name: string
          description: string | null
          price: number
          image_url: string | null
          is_available: boolean
          is_featured: boolean
          allergens: string[] | null
          dietary_tags: string[] | null
          preparation_time: number | null
          calories: number | null
          display_order: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          category_id: string
          name: string
          description?: string | null
          price: number
          image_url?: string | null
          is_available?: boolean
          is_featured?: boolean
          allergens?: string[] | null
          dietary_tags?: string[] | null
          preparation_time?: number | null
          calories?: number | null
          display_order?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          category_id?: string
          name?: string
          description?: string | null
          price?: number
          image_url?: string | null
          is_available?: boolean
          is_featured?: boolean
          allergens?: string[] | null
          dietary_tags?: string[] | null
          preparation_time?: number | null
          calories?: number | null
          display_order?: number
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "menu_items_category_id_fkey"
            columns: ["category_id"]
            referencedRelation: "menu_categories"
            referencedColumns: ["id"]
          }
        ]
      }
      subscriptions: {
        Row: {
          id: string
          organization_id: string
          stripe_subscription_id: string
          stripe_customer_id: string
          status: 'incomplete' | 'incomplete_expired' | 'trialing' | 'active' | 'past_due' | 'canceled' | 'unpaid'
          price_id: string
          product_id: string
          current_period_start: string
          current_period_end: string
          trial_start: string | null
          trial_end: string | null
          canceled_at: string | null
          cancel_at: string | null
          cancel_at_period_end: boolean
          metadata: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          stripe_subscription_id: string
          stripe_customer_id: string
          status: 'incomplete' | 'incomplete_expired' | 'trialing' | 'active' | 'past_due' | 'canceled' | 'unpaid'
          price_id: string
          product_id: string
          current_period_start: string
          current_period_end: string
          trial_start?: string | null
          trial_end?: string | null
          canceled_at?: string | null
          cancel_at?: string | null
          cancel_at_period_end?: boolean
          metadata?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          stripe_subscription_id?: string
          stripe_customer_id?: string
          status?: 'incomplete' | 'incomplete_expired' | 'trialing' | 'active' | 'past_due' | 'canceled' | 'unpaid'
          price_id?: string
          product_id?: string
          current_period_start?: string
          current_period_end?: string
          trial_start?: string | null
          trial_end?: string | null
          canceled_at?: string | null
          cancel_at?: string | null
          cancel_at_period_end?: boolean
          metadata?: Json
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "subscriptions_organization_id_fkey"
            columns: ["organization_id"]
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          }
        ]
      }
      subscription_items: {
        Row: {
          id: string
          subscription_id: string
          stripe_subscription_item_id: string
          price_id: string
          product_id: string
          quantity: number
          created_at: string
        }
        Insert: {
          id?: string
          subscription_id: string
          stripe_subscription_item_id: string
          price_id: string
          product_id: string
          quantity?: number
          created_at?: string
        }
        Update: {
          id?: string
          subscription_id?: string
          stripe_subscription_item_id?: string
          price_id?: string
          product_id?: string
          quantity?: number
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "subscription_items_subscription_id_fkey"
            columns: ["subscription_id"]
            referencedRelation: "subscriptions"
            referencedColumns: ["id"]
          }
        ]
      }
      invoices: {
        Row: {
          id: string
          organization_id: string
          subscription_id: string | null
          stripe_invoice_id: string
          stripe_customer_id: string
          amount_paid: number
          amount_due: number
          amount_remaining: number
          currency: string
          status: 'draft' | 'open' | 'paid' | 'uncollectible' | 'void'
          hosted_invoice_url: string | null
          invoice_pdf_url: string | null
          period_start: string | null
          period_end: string | null
          created_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          subscription_id?: string | null
          stripe_invoice_id: string
          stripe_customer_id: string
          amount_paid: number
          amount_due: number
          amount_remaining?: number
          currency?: string
          status: 'draft' | 'open' | 'paid' | 'uncollectible' | 'void'
          hosted_invoice_url?: string | null
          invoice_pdf_url?: string | null
          period_start?: string | null
          period_end?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          subscription_id?: string | null
          stripe_invoice_id?: string
          stripe_customer_id?: string
          amount_paid?: number
          amount_due?: number
          amount_remaining?: number
          currency?: string
          status?: 'draft' | 'open' | 'paid' | 'uncollectible' | 'void'
          hosted_invoice_url?: string | null
          invoice_pdf_url?: string | null
          period_start?: string | null
          period_end?: string | null
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "invoices_organization_id_fkey"
            columns: ["organization_id"]
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoices_subscription_id_fkey"
            columns: ["subscription_id"]
            referencedRelation: "subscriptions"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
} 