import { getOrganizations, testDatabaseConnection, testAllTablesAccess } from '@/lib/database/queries'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, XCircle, Database, Building, AlertTriangle, Info } from 'lucide-react'

export default async function TestDatabasePage() {
  // Test database connection
  const connectionTest = await testDatabaseConnection()
  
  // Test all tables access
  const tablesTest = await testAllTablesAccess()
  
  // Get organizations to display seed data
  let organizations: any[] = []
  try {
    organizations = await getOrganizations()
  } catch (error) {
    console.error('Error fetching organizations:', error)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-green-50 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Database Connection Test
          </h1>
          <p className="text-xl text-gray-600">
            Testing Supabase integration and displaying seed data
          </p>
        </div>

        {/* Connection Test Result */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Database Connection Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-3 mb-4">
              {connectionTest.success ? (
                <>
                  <CheckCircle className="h-6 w-6 text-green-500" />
                  <div>
                    <p className="font-medium text-green-700">
                      Connection Successful
                    </p>
                    <p className="text-sm text-gray-600">
                      {connectionTest.message}
                    </p>
                    {connectionTest.connectionType && (
                      <Badge variant="secondary" className="mt-1">
                        {connectionTest.connectionType}
                      </Badge>
                    )}
                  </div>
                </>
              ) : (
                <>
                  <XCircle className="h-6 w-6 text-red-500" />
                  <div>
                    <p className="font-medium text-red-700">
                      Connection Failed
                    </p>
                    <p className="text-sm text-gray-600">
                      {connectionTest.error}
                    </p>
                  </div>
                </>
              )}
            </div>
            
            {/* Sample Data Preview */}
            {connectionTest.sampleData && connectionTest.sampleData.length > 0 && (
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <h4 className="font-medium text-sm text-gray-700 mb-2">Sample Data:</h4>
                <pre className="text-xs text-gray-600 overflow-x-auto">
                  {JSON.stringify(connectionTest.sampleData, null, 2)}
                </pre>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Setup Instructions */}
        {!connectionTest.success && (
          <Card className="mb-8 border-orange-200 bg-orange-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-orange-800">
                <AlertTriangle className="h-5 w-5" />
                Setup Required
              </CardTitle>
            </CardHeader>
            <CardContent className="text-orange-700">
              <p className="mb-4">
                To fix the connection issue, you need to set up your Supabase project:
              </p>
              <ol className="list-decimal list-inside space-y-2 text-sm">
                <li>Go to <a href="https://supabase.com/dashboard" target="_blank" rel="noopener noreferrer" className="underline">supabase.com/dashboard</a></li>
                <li>Create a new project (name: "restaurant-saas")</li>
                <li>Wait for the project to be ready (2-3 minutes)</li>
                <li>Go to Project Settings → API</li>
                <li>Copy your project URL and API keys</li>
                <li>Update your <code className="bg-orange-100 px-1 rounded">.env.local</code> file with the actual credentials</li>
                <li>Run the SQL from <code className="bg-orange-100 px-1 rounded">docs/database-schema.sql</code> in Supabase SQL Editor</li>
                <li>Restart your development server</li>
              </ol>
            </CardContent>
          </Card>
        )}

        {/* Table Access Test */}
        {tablesTest.success && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Database Tables Access Test
              </CardTitle>
              <CardDescription>
                Testing access to all database tables
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
                {tablesTest.results && Object.entries(tablesTest.results).map(([table, result]: [string, any]) => (
                  <div key={table} className="flex items-center gap-2 p-2 border rounded">
                    {result.success ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-500" />
                    )}
                    <div className="min-w-0 flex-1">
                      <p className="text-sm font-medium truncate">{table}</p>
                      {result.error && (
                        <p className="text-xs text-red-600 truncate">{result.error}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Organizations Data */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              Seed Data - Organizations
            </CardTitle>
            <CardDescription>
              Sample restaurant organizations from our database
            </CardDescription>
          </CardHeader>
          <CardContent>
            {organizations.length > 0 ? (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {organizations.map((org) => (
                  <div key={org.id} className="border rounded-lg p-4 bg-white">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="font-semibold text-lg">{org.name}</h3>
                      <Badge 
                        variant={org.subscription_status === 'active' ? 'default' : 'secondary'}
                      >
                        {org.subscription_status}
                      </Badge>
                    </div>
                    
                    <div className="space-y-2 text-sm text-gray-600">
                      <p>
                        <span className="font-medium">Slug:</span> {org.slug}
                      </p>
                      {org.description && (
                        <p>
                          <span className="font-medium">Description:</span> {org.description}
                        </p>
                      )}
                      {org.contact_email && (
                        <p>
                          <span className="font-medium">Email:</span> {org.contact_email}
                        </p>
                      )}
                      {org.plan_id && (
                        <p>
                          <span className="font-medium">Plan:</span> {org.plan_id}
                        </p>
                      )}
                      <p className="text-xs">
                        Created: {new Date(org.created_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500 mb-2">No organizations found</p>
                <p className="text-sm text-gray-400">
                  Run the database schema setup to add sample data
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Database Schema Info */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Database Schema Information</CardTitle>
            <CardDescription>
              Current database tables and their purpose
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <h4 className="font-medium">Core Tables</h4>
                <ul className="text-sm space-y-1">
                  <li>• <strong>organizations</strong> - Restaurant companies</li>
                  <li>• <strong>users</strong> - Team members and access</li>
                  <li>• <strong>menus</strong> - Restaurant menu containers</li>
                  <li>• <strong>menu_categories</strong> - Menu sections</li>
                  <li>• <strong>menu_items</strong> - Individual dishes</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">Billing Tables</h4>
                <ul className="text-sm space-y-1">
                  <li>• <strong>subscriptions</strong> - Stripe subscriptions</li>
                  <li>• <strong>subscription_items</strong> - Billing line items</li>
                  <li>• <strong>invoices</strong> - Payment history</li>
                </ul>
              </div>
            </div>
            
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium mb-2">Security Features</h4>
              <ul className="text-sm space-y-1">
                <li>✅ Row Level Security (RLS) enabled on all tables</li>
                <li>✅ Multi-tenant data isolation</li>
                <li>✅ Public menu access for customers</li>
                <li>✅ Role-based access control</li>
                <li>✅ Service role bypass for admin operations</li>
              </ul>
            </div>

            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <Info className="h-4 w-4" />
                Next Steps
              </h4>
              <ul className="text-sm space-y-1">
                <li>1. Set up your Supabase project credentials</li>
                <li>2. Run the database schema SQL in Supabase SQL Editor</li>
                <li>3. Test the connection again to see seed data</li>
                <li>4. Begin building your restaurant management features</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 